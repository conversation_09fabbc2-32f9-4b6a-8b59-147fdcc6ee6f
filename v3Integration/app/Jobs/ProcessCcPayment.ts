import { JobContract } from '@ioc:Rocketseat/Bull'
import { setAPIAuth } from '@/utils/apiAuth'
import Contact from '../Models/Contact'
import { cLog } from '@/utils'
import { syncInvoice } from '@/app/helpers/ap'

/*
|--------------------------------------------------------------------------
| Job setup
|--------------------------------------------------------------------------
|
| This is the basic setup for creating a job, but you can override
| some settings.
|
| You can get more details by looking at the bullmq documentation.
| https://docs.bullmq.io/
*/
// TODO: Most important
/**
 * We have to calculate credit amount (If a payment don't have invoicePayments, We can count it as credit amount, this one isn't attatched with any invoice {invoicePayments property should be empty})
 *
 * Calculate LTV by
 * filter payments if the reversedBy, reverses is null and cancled is false
 * sum the gross amount (this is LTV)
 *
 * Calculate due amount
 * Get all the open invoice
 * All Invoice amount = invoice positions (gross amounts - position discount) - invoice discount
 * Due amount = All Invoice amount - LTV
 */

export default class ProcessCcPayment implements JobContract {
  public key = 'ProcessCcPayment'

  public async handle(job) {
    const { payload, auth: oAuth } = job.data
    await setAPIAuth(oAuth)
    cLog(`Patient`, payload)
    const contact = await Contact.findBy('ccId', payload.patient)
    //contact && contact.apId && (await this.updateCustomfields(contact, payload))
    if (!contact) {
      cLog(`Patient not synced with AP yet. Patient ID: ${payload.patient}`)
    }
    contact && contact.apId && contact.ccId && (await syncInvoice(contact))
  }
}
