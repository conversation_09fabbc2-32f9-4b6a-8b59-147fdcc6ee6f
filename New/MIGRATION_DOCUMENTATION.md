# DermaCare Data Sync Service - Migration Documentation

## Overview

This document describes the migration from the v3Integration socket-based system to a modern webhook-based architecture compatible with Cloudflare Workers. The new implementation maintains the same business logic while providing improved performance, reliability, and maintainability.

## Architecture Changes

### From Socket.io to Webhooks

**v3Integration (Old):**

- Used socket.io-client to listen for real-time events
- Events: `mobimed:App\\Events\\EntityWasCreated`, `mobimed:App\\Events\\EntityWasUpdated`, etc.
- Bull queue system for job processing

**New Implementation:**

- HTTP webhook endpoints for receiving events
- Standardized webhook event format
- Direct processing without queue system (suitable for Cloudflare Workers)

### Webhook Event Format

```json
{
  "event": "EntityWasCreated",
  "model": "Patient",
  "id": 123,
  "payload": { "firstName": "..." }
}
```

### Buffer Management

**v3Integration (Old):**

- Used MySQL `skips` table to prevent duplicate processing
- Database-based duplicate detection

**New Implementation:**

- In-memory buffer using Map for O(1) lookup performance
- Configurable buffer time (default: 60 seconds)
- `isItInBuffer()` utility method returns boolean

## API Structure

### Endpoints

- `POST /webhook` - Main webhook endpoint for CC events
- `POST /:location/ap/appointment/create` - AP appointment creation webhook
- `POST /:location/ap/appointment/update` - AP appointment update webhook
- `POST /:location/ap/appointment/delete` - AP appointment deletion webhook
- `POST /:location/ap/contact/create` - AP contact creation webhook
- `POST /:location/ap/contact/update` - AP contact update webhook
- `POST /:location/ap/contact/delete` - AP contact deletion webhook
- `GET /health` - Health check with buffer statistics

### Request Flow

1. Webhook receives event
2. Validates event structure
3. Checks buffer for duplicate processing
4. Routes to appropriate processor
5. Updates database and syncs to external APIs
6. Returns processing result

## Core Components

### 1. Webhook Handler (`src/webhook/handler.ts`)

**Key Functions:**

- `handleWebhookEvent()` - Main webhook processing entry point
- `routeWebhookEvent()` - Routes events to appropriate processors
- `isValidWebhookEvent()` - Validates incoming webhook structure

### 2. Buffer Manager (`src/utils/bufferManager.ts`)

**Key Functions:**

- `isItInBuffer(bufferKey)` - Checks if event is in processing buffer
- `addToBuffer(bufferKey)` - Adds event to buffer
- `checkAndAddToBuffer(bufferKey)` - Atomic check and add operation

### 3. API Clients (`src/api/`)

**AP Client (`apClient.ts`):**

- Contact operations (create, update, get, delete)
- Appointment operations (create, update, delete)
- Custom field operations
- Note operations

**CC Client (`ccClient.ts`):**

- Patient operations (create, update, get, search)
- Appointment operations (create, update, cancel, delete)
- Invoice and payment operations
- Custom field and location operations

### 4. Processors (`src/processors/`)

**Patient Processor (`patientProcessor.ts`):**

- `processPatientCreate()` - Equivalent to ProcessPatientCreate job
- `processPatientUpdate()` - Equivalent to ProcessPatientUpdate job

**Appointment Processor (`appointmentProcessor.ts`):**

- `processAppointmentCreate()` - Equivalent to ProcessCcAppointmentCreate job
- `processAppointmentUpdate()` - Equivalent to ProcessCcAppointmentUpdate job
- `processAppointmentDelete()` - Equivalent to ProcessCcAppointmentDelete job

**Invoice/Payment Processor (`invoicePaymentProcessor.ts`):**

- `processInvoicePayment()` - Equivalent to ProcessInvoicePayment job
- Syncs invoice and payment data to AP custom fields

### 5. Helper Functions (`src/helpers/`)

**Data Transform (`dataTransform.ts`):**

- `transformCCPatientToAPContact()` - Converts CC patient to AP contact format
- `transformAPContactToCCPatient()` - Converts AP contact to CC patient format
- `removeHtmlTags()` - Strips HTML from text content
- `formatDateToISO()` - Standardizes date formatting

**Custom Fields (`customFields.ts`):**

- `getAPCustomFieldNameToIdMap()` - Cached mapping of AP field names to IDs
- `getOrCreateAPCustomFieldId()` - Creates AP custom fields if they don't exist
- `updateAPContactCustomFields()` - Updates AP contact with custom field data

## Database Schema

The new implementation uses the existing schema with these key tables:

- `patients` - Stores patient data from both CC and AP
- `appointments` - Stores appointment data from both systems
- `ap_custom_fields` - Caches AP custom field configurations
- `cc_custom_fields` - Caches CC custom field configurations
- `error_logs` - Comprehensive error logging with deduplication

## Error Handling

### Error Logging (`src/utils/errorLogger.ts`)

**Features:**

- Automatic deduplication (prevents spam)
- Structured error data with metadata
- Service-specific error categorization
- Stack trace preservation

**Key Functions:**

- `logError()` - General error logging
- `logSyncError()` - Specialized for sync operations

### Error Categories

- `WEBHOOK_VALIDATION_ERROR` - Invalid webhook structure
- `WEBHOOK_PROCESSING_ERROR` - General webhook processing failures
- `PATIENT_CREATE_ERROR` - Patient creation failures
- `APPOINTMENT_UPDATE_ERROR` - Appointment update failures
- `API_REQUEST_ERROR` - External API communication failures

## Configuration

### Environment Variables (via `src/utils/configs.ts`)

```typescript
interface AppConfigs {
  databaseUrl: string;
  ccApiDomain: string;
  ccApiKey: string;
  apApiKey: string;
  apApiDomain: string;
  cacheTTL: number;
  maxRetries: number;
  requestTimeout: number;
  locationID: string;
  syncBufferTimeSec: number;
  apCalendarId: string;
}
```

## Performance Optimizations

### 1. Caching

- Custom field mappings cached in memory and database
- Automatic cache refresh on miss
- Configurable TTL for cache entries

### 2. Request Optimization

- Exponential backoff retry logic
- Request timeout handling
- Connection pooling for database operations

### 3. Buffer Management

- O(1) lookup performance using Map
- Automatic cleanup of expired entries
- Configurable buffer time

## Migration Checklist

### ✅ Completed Features

1. **Webhook Infrastructure**

   - HTTP endpoint for receiving events
   - Event validation and routing
   - Error handling and logging

2. **Buffer Management**

   - `isItInBuffer()` utility method
   - In-memory buffer with automatic cleanup
   - Configurable buffer time

3. **API Request Utilities**

   - Cloudflare Workers compatible HTTP client
   - Retry logic with exponential backoff
   - Proper error handling and logging

4. **Patient Processing**

   - Patient create and update logic
   - Data transformation between CC and AP
   - Custom field synchronization

5. **Appointment Processing**

   - Appointment create, update, and delete logic
   - Cross-platform synchronization
   - Error handling and recovery

6. **Invoice/Payment Processing**

   - Invoice and payment data synchronization
   - Custom field updates in AP
   - LTV (Lifetime Value) calculations

7. **Helper Functions**

   - Data transformation utilities
   - Custom field management
   - Validation and formatting functions

8. **Error Logging**
   - Comprehensive error tracking
   - Deduplication logic
   - Structured error data

### ❌ Excluded Features (As Requested)

1. **Slack Logging** - Removed as per requirements
2. **OAuth Implementation** - Excluded as specified
3. **Socket.io Dependencies** - Replaced with webhooks

## Testing Recommendations

### Unit Tests

- Test webhook event validation
- Test buffer management logic
- Test data transformation functions
- Test API client error handling

### Integration Tests

- Test end-to-end webhook processing
- Test database operations
- Test external API communication
- Test error logging functionality

### Performance Tests

- Test webhook endpoint under load
- Test buffer performance with high volume
- Test API client retry logic
- Test database query performance

## Deployment Notes

### Cloudflare Workers Compatibility

- Uses only Web APIs (no Node.js specific APIs)
- Optimized for edge computing
- Minimal memory footprint
- Fast cold start times

### Environment Setup

- Configure database connection string
- Set API keys for CC and AP
- Configure buffer time and retry settings
- Set up error monitoring

## Monitoring and Observability

### Health Checks

- `/health` endpoint provides system status
- Buffer statistics included in health response
- Database connectivity verification

### Error Tracking

- All errors logged to database
- Structured error data for analysis
- Service-specific error categorization

### Performance Metrics

- Buffer hit/miss rates
- API response times
- Processing success rates
- Error frequency by type

## Business Logic Equivalence

### Patient Processing

The new implementation maintains exact business logic equivalence with v3Integration:

**ProcessPatientCreate → processPatientCreate()**

- Validates email/phone requirements
- Checks for existing patients
- Creates/updates database records
- Syncs to AP using upsert logic
- Updates custom fields

**ProcessPatientUpdate → processPatientUpdate()**

- Buffer check for duplicate processing
- Data validation and transformation
- Database record updates
- AP contact synchronization
- Custom field updates

### Appointment Processing

**ProcessCcAppointmentCreate → processAppointmentCreate()**

- Patient validation and lookup
- Database record creation
- AP appointment creation
- Cross-reference maintenance

**ProcessCcAppointmentUpdate → processAppointmentUpdate()**

- Appointment lookup and validation
- Database updates
- AP appointment synchronization
- Error handling and recovery

**ProcessCcAppointmentDelete → processAppointmentDelete()**

- Appointment lookup
- AP deletion (with error tolerance)
- Database cleanup

### Invoice/Payment Processing

**ProcessInvoicePayment → processInvoicePayment()**

- Patient lookup and validation
- Invoice/payment data retrieval
- Custom field calculations
- AP contact updates with financial data

## Custom Field Mapping

The system maintains the same custom field mappings as v3Integration:

### Invoice Fields

- Latest Invoice PDF URL
- Latest Gross Amount
- Latest Discount
- Latest Total Amount
- Latest Products
- Latest Diagnosis
- Latest Treated By

### Payment Fields

- Latest Payment Status
- Latest Amount Paid
- Latest Payment Date
- Latest Payment PDF URL

### LTV Fields

- LTV (Lifetime Value)
- Total Invoice Amount
- Total Paid Amount
- Total Due Amount
