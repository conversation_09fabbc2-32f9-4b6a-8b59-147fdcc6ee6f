/**
 * API module exports
 * Provides centralized access to all API clients and utilities
 */

// Export API clients
export { apClient } from "./apClient";
export { ccClient } from "./ccClient";

// Export request utilities
export {
	ApiError,
	type ApiResponse,
	createApiClient,
	extractResponseData,
	makeRequest,
	type RequestOptions,
} from "./request";

// Re-export for convenience
export const api = {
	ap: () => import("./apClient").then((m) => m.apClient),
	cc: () => import("./ccClient").then((m) => m.ccClient),
};
