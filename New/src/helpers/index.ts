/**
 * Helper functions module exports
 * Provides centralized access to all helper utilities
 */

// Export custom field utilities
export {
	clearCustomFieldCaches,
	extractCCCustomFieldValues as extractCCPatientCustomFieldValues,
	getAPCustomFieldNameToIdMap,
	getCCCustomFieldIdToConfigMap,
	getCustomFieldCacheStats,
	getOrCreateAPCustomFieldId,
	prepareAPCustomFieldUpdates,
	refreshCustomFieldCaches,
	updateAPContactCustomFields,
} from "./customFields";
// Export data transformation utilities
export {
	calculateFinancialMetrics,
	calculateServiceAppointmentCounts,
	calculateServiceSpending,
	extractCCCustomFieldValues,
	extractLatestInvoiceData,
	extractLatestPaymentData,
	generateAdvancedCustomFieldData,
	reduceCustomFieldValue,
	removeHtmlTags,
	safeJsonParse,
	safeJsonStringify,
	transformAPContactToCCPatient,
	transformCCPatientToAPContact,
} from "./dataTransform";

// Note: Validation functions (isValidEmail, isValidPhone, formatPhoneNumber, formatDateToISO)
// are now available from @utils/validation for consistency

// Re-export for convenience
export const helpers = {
	dataTransform: () => import("./dataTransform"),
	customFields: () => import("./customFields"),
};
