/**
 * Financial data processing for DermaCare bi-directional sync service
 *
 * This module provides comprehensive financial data management functionality
 * that handles invoice and payment processing between CliniCore (CC) and
 * AutoPatient (AP) platforms. It replicates the financial data handling
 * capabilities from v3Integration while leveraging the improved architecture.
 *
 * **Key Features:**
 * - Invoice data retrieval and processing from CC platform
 * - Payment data retrieval and processing from CC platform
 * - Financial metrics calculation (LTV, spending, payment history)
 * - Integration with custom field synchronization for financial data
 * - Comprehensive error handling and logging
 *
 * **Financial Data Types:**
 * - Invoice records with line items and totals
 * - Payment records with methods and amounts
 * - Financial summaries and metrics
 * - Spending analysis by service categories
 * - Lifetime value calculations
 *
 * **Integration Points:**
 * - Links with patient records for financial history
 * - Connects to CC API for invoice/payment retrieval
 * - Supports custom field updates with financial metrics
 * - Enables financial reporting and analytics
 *
 * @example
 * ```typescript
 * // Get patient financial data
 * const financialData = await getPatientFinancialData(patientRecord);
 *
 * // Process invoice data
 * const invoices = await processPatientInvoices(ccPatientData);
 *
 * // Calculate financial metrics
 * const metrics = await calculateFinancialMetrics(patientRecord);
 * ```
 *
 * @since 1.0.0
 * @version 1.0.0
 */

import { ccClient } from "@api/ccClient";
import { patient } from "@database/schema";
import type {
	GetCCPatientType,
} from "@type";
import { logError } from "@utils/errorLogger";

/**
 * Invoice data interface
 */
export interface InvoiceData {
	id: number;
	patientId: number;
	amount: number;
	status: string;
	dateCreated: string;
	datePaid?: string;
	lineItems: InvoiceLineItem[];
}

/**
 * Invoice line item interface
 */
export interface InvoiceLineItem {
	id: number;
	serviceId: number;
	serviceName: string;
	quantity: number;
	unitPrice: number;
	totalPrice: number;
}

/**
 * Payment data interface
 */
export interface PaymentData {
	id: number;
	patientId: number;
	invoiceId?: number;
	amount: number;
	method: string;
	status: string;
	dateCreated: string;
	dateProcessed?: string;
}

/**
 * Financial metrics interface
 */
export interface FinancialMetrics {
	totalSpent: number;
	totalInvoices: number;
	totalPayments: number;
	averageInvoiceAmount: number;
	averagePaymentAmount: number;
	lifetimeValue: number;
	lastPaymentDate?: string;
	lastInvoiceDate?: string;
	paymentMethods: Record<string, number>;
	spendingByService: Record<string, number>;
}

/**
 * Retrieves and processes invoice data for a patient
 *
 * This function fetches invoice data from the CC platform for a specific patient
 * and processes it into a standardized format for use in financial calculations
 * and custom field synchronization.
 *
 * **Data Processing:**
 * - Fetches invoice records from CC API
 * - Processes line items and calculates totals
 * - Handles invoice status and payment information
 * - Formats data for financial metrics calculation
 *
 * @param patientRecord - Patient record from local database
 * @returns Promise resolving to array of invoice data
 */
export async function getPatientInvoices(
	patientRecord: typeof patient.$inferSelect,
): Promise<InvoiceData[]> {
	try {
		if (!patientRecord.ccId || !patientRecord.ccData) {
			console.log(`Patient ${patientRecord.id} has no CC data, skipping invoice retrieval`);
			return [];
		}

		const ccPatientData = patientRecord.ccData as GetCCPatientType;
		
		// Check if patient has invoice IDs in CC data
		if (!ccPatientData.invoices || ccPatientData.invoices.length === 0) {
			console.log(`Patient ${patientRecord.ccId} has no invoices`);
			return [];
		}

		console.log(`Retrieving ${ccPatientData.invoices.length} invoices for patient ${patientRecord.ccId}`);

		// Fetch invoice data from CC API
		const invoices: InvoiceData[] = [];
		
		for (const invoiceId of ccPatientData.invoices) {
			try {
				const invoice = await ccClient.invoice.get(invoiceId);
				if (invoice) {
					invoices.push({
						id: invoice.id,
						patientId: patientRecord.ccId,
						amount: invoice.positions?.reduce((sum, pos) => sum + (pos.gross || 0), 0) || 0,
						status: invoice.status || 'unknown',
						dateCreated: invoice.createdAt || new Date().toISOString(),
						datePaid: undefined, // Payment date not available in invoice structure
						lineItems: invoice.positions?.map((item) => ({
							id: item.id,
							serviceId: item.originalService || 0,
							serviceName: item.name || 'Unknown Service',
							quantity: item.count || 1,
							unitPrice: (item.gross || 0) / (item.count || 1),
							totalPrice: item.gross || 0,
						})) || [],
					});
				}
			} catch (invoiceError) {
				console.error(`Failed to fetch invoice ${invoiceId}:`, invoiceError);
				// Continue with other invoices
			}
		}

		console.log(`Retrieved ${invoices.length} invoices for patient ${patientRecord.ccId}`);
		return invoices;
	} catch (error) {
		await logError(
			"PATIENT_INVOICES_RETRIEVAL_ERROR",
			error,
			{
				patientId: patientRecord.id,
				ccId: patientRecord.ccId,
			},
			"FinancialData",
		);
		return [];
	}
}

/**
 * Retrieves and processes payment data for a patient
 *
 * This function fetches payment data from the CC platform for a specific patient
 * and processes it into a standardized format for financial analysis.
 *
 * @param patientRecord - Patient record from local database
 * @returns Promise resolving to array of payment data
 */
export async function getPatientPayments(
	patientRecord: typeof patient.$inferSelect,
): Promise<PaymentData[]> {
	try {
		if (!patientRecord.ccId || !patientRecord.ccData) {
			console.log(`Patient ${patientRecord.id} has no CC data, skipping payment retrieval`);
			return [];
		}

		const ccPatientData = patientRecord.ccData as GetCCPatientType;
		
		// Check if patient has payment IDs in CC data
		if (!ccPatientData.payments || ccPatientData.payments.length === 0) {
			console.log(`Patient ${patientRecord.ccId} has no payments`);
			return [];
		}

		console.log(`Retrieving ${ccPatientData.payments.length} payments for patient ${patientRecord.ccId}`);

		// Fetch payment data from CC API
		const payments: PaymentData[] = [];
		
		for (const paymentId of ccPatientData.payments) {
			try {
				const payment = await ccClient.payment.get(paymentId);
				if (payment) {
					payments.push({
						id: payment.id,
						patientId: patientRecord.ccId,
						invoiceId: payment.invoicePayments?.[0]?.invoice || undefined,
						amount: payment.gross || 0,
						method: 'unknown', // Method not available in payment structure
						status: payment.canceled ? 'canceled' : 'completed',
						dateCreated: payment.createdAt || new Date().toISOString(),
						dateProcessed: payment.date || undefined,
					});
				}
			} catch (paymentError) {
				console.error(`Failed to fetch payment ${paymentId}:`, paymentError);
				// Continue with other payments
			}
		}

		console.log(`Retrieved ${payments.length} payments for patient ${patientRecord.ccId}`);
		return payments;
	} catch (error) {
		await logError(
			"PATIENT_PAYMENTS_RETRIEVAL_ERROR",
			error,
			{
				patientId: patientRecord.id,
				ccId: patientRecord.ccId,
			},
			"FinancialData",
		);
		return [];
	}
}

/**
 * Calculates comprehensive financial metrics for a patient
 *
 * This function processes invoice and payment data to generate financial
 * metrics that can be used for custom field synchronization and reporting.
 *
 * @param patientRecord - Patient record from local database
 * @returns Promise resolving to financial metrics
 */
export async function calculatePatientFinancialMetrics(
	patientRecord: typeof patient.$inferSelect,
): Promise<FinancialMetrics> {
	try {
		console.log(`Calculating financial metrics for patient ${patientRecord.id}`);

		// Get invoice and payment data
		const [invoices, payments] = await Promise.all([
			getPatientInvoices(patientRecord),
			getPatientPayments(patientRecord),
		]);

		// Calculate basic metrics
		const totalInvoices = invoices.length;
		const totalPayments = payments.length;
		const totalInvoiceAmount = invoices.reduce((sum, invoice) => sum + invoice.amount, 0);
		const totalPaymentAmount = payments.reduce((sum, payment) => sum + payment.amount, 0);

		// Calculate averages
		const averageInvoiceAmount = totalInvoices > 0 ? totalInvoiceAmount / totalInvoices : 0;
		const averagePaymentAmount = totalPayments > 0 ? totalPaymentAmount / totalPayments : 0;

		// Find latest dates
		const lastInvoiceDate = invoices.length > 0 
			? invoices.sort((a, b) => new Date(b.dateCreated).getTime() - new Date(a.dateCreated).getTime())[0].dateCreated
			: undefined;
		
		const lastPaymentDate = payments.length > 0
			? payments.sort((a, b) => new Date(b.dateCreated).getTime() - new Date(a.dateCreated).getTime())[0].dateCreated
			: undefined;

		// Calculate payment methods breakdown
		const paymentMethods: Record<string, number> = {};
		payments.forEach(payment => {
			paymentMethods[payment.method] = (paymentMethods[payment.method] || 0) + payment.amount;
		});

		// Calculate spending by service
		const spendingByService: Record<string, number> = {};
		invoices.forEach(invoice => {
			invoice.lineItems.forEach(item => {
				spendingByService[item.serviceName] = (spendingByService[item.serviceName] || 0) + item.totalPrice;
			});
		});

		// Calculate lifetime value (total paid amount)
		const lifetimeValue = totalPaymentAmount;

		const metrics: FinancialMetrics = {
			totalSpent: totalPaymentAmount,
			totalInvoices,
			totalPayments,
			averageInvoiceAmount,
			averagePaymentAmount,
			lifetimeValue,
			lastPaymentDate,
			lastInvoiceDate,
			paymentMethods,
			spendingByService,
		};

		console.log(`Financial metrics calculated for patient ${patientRecord.id}:`, {
			totalSpent: metrics.totalSpent,
			totalInvoices: metrics.totalInvoices,
			totalPayments: metrics.totalPayments,
			lifetimeValue: metrics.lifetimeValue,
		});

		return metrics;
	} catch (error) {
		await logError(
			"FINANCIAL_METRICS_CALCULATION_ERROR",
			error,
			{
				patientId: patientRecord.id,
				ccId: patientRecord.ccId,
			},
			"FinancialData",
		);

		// Return empty metrics on error
		return {
			totalSpent: 0,
			totalInvoices: 0,
			totalPayments: 0,
			averageInvoiceAmount: 0,
			averagePaymentAmount: 0,
			lifetimeValue: 0,
			paymentMethods: {},
			spendingByService: {},
		};
	}
}

/**
 * Generates financial custom field data for synchronization
 *
 * This function creates a set of custom field data based on financial metrics
 * that can be synchronized to the AP platform for marketing and communication purposes.
 *
 * @param patientRecord - Patient record from local database
 * @returns Promise resolving to custom field data object
 */
export async function generateFinancialCustomFields(
	patientRecord: typeof patient.$inferSelect,
): Promise<Record<string, string | number>> {
	try {
		const metrics = await calculatePatientFinancialMetrics(patientRecord);

		const customFields: Record<string, string | number> = {
			"Total Spent": metrics.totalSpent,
			"Total Invoices": metrics.totalInvoices,
			"Total Payments": metrics.totalPayments,
			"Average Invoice Amount": Math.round(metrics.averageInvoiceAmount * 100) / 100,
			"Average Payment Amount": Math.round(metrics.averagePaymentAmount * 100) / 100,
			"Lifetime Value": metrics.lifetimeValue,
		};

		// Add date fields if available
		if (metrics.lastPaymentDate) {
			customFields["Last Payment Date"] = new Date(metrics.lastPaymentDate).toLocaleDateString();
		}

		if (metrics.lastInvoiceDate) {
			customFields["Last Invoice Date"] = new Date(metrics.lastInvoiceDate).toLocaleDateString();
		}

		// Add top payment method
		const topPaymentMethod = Object.entries(metrics.paymentMethods)
			.sort(([,a], [,b]) => b - a)[0];
		if (topPaymentMethod) {
			customFields["Primary Payment Method"] = topPaymentMethod[0];
		}

		// Add top service spending
		const topService = Object.entries(metrics.spendingByService)
			.sort(([,a], [,b]) => b - a)[0];
		if (topService) {
			customFields["Top Service Spending"] = `${topService[0]}: $${topService[1]}`;
		}

		console.log(`Generated ${Object.keys(customFields).length} financial custom fields for patient ${patientRecord.id}`);
		return customFields;
	} catch (error) {
		await logError(
			"FINANCIAL_CUSTOM_FIELDS_GENERATION_ERROR",
			error,
			{
				patientId: patientRecord.id,
				ccId: patientRecord.ccId,
			},
			"FinancialData",
		);
		return {};
	}
}
