/**
 * Storage module exports for DermaCare bi-directional sync service
 *
 * This module provides centralized access to all storage utilities and functions
 * that implement the critical missing functionality to achieve complete feature
 * parity with the v3Integration system.
 *
 * **Key Exports:**
 * - Patient storage utilities with smart upsert logic
 * - Custom field synchronization for bi-directional sync
 * - Appointment note management for AP platform
 * - Financial data processing and metrics calculation
 * - Relationship query helpers for advanced operations
 *
 * **Usage:**
 * Import specific functions or use the grouped exports for organized access
 * to different categories of storage functionality.
 *
 * @example
 * ```typescript
 * // Import specific functions
 * import { searchCreateOrUpdatePatient, syncCCtoAPCustomFields } from '@storage';
 *
 * // Import grouped functionality
 * import { patientStorage, customFieldSync } from '@storage';
 * ```
 *
 * @since 1.0.0
 * @version 1.0.0
 */

// Patient storage utilities
import {
	searchCreateOrUpdatePatient,
	findPatientByMultipleCriteria,
	type PatientUpsertPayload,
} from "./patientStorage";

// Custom field synchronization
import {
	syncCCtoAPCustomFields,
	syncAPtoCCCustomFields,
	syncAppointment<PERSON>ustomFieldsToAP,
} from "./customFieldSync";

// Re-export for external use
export {
	searchCreateOrUpdatePatient,
	findPatientByMultipleCriteria,
	type PatientUpsertPayload,
	syncCCtoAPCustomFields,
	syncAPtoCCCustomFields,
	syncAppointmentCustomFieldsToAP,
};

// Appointment note management
export {
	createAppointmentNote,
	updateAppointmentNote,
	deleteAppointmentNote,
	updateAppointmentWithNoteId,
	type AppointmentNoteEvent,
	type NoteTemplate,
} from "./appointmentNotes";

// Financial data processing
export {
	getPatientInvoices,
	getPatientPayments,
	calculatePatientFinancialMetrics,
	generateFinancialCustomFields,
	type InvoiceData,
	type PaymentData,
	type FinancialMetrics,
	type InvoiceLineItem,
} from "./financialData";

// Grouped exports for organized access
export const patientStorage = {
	searchCreateOrUpdate: () => import("./patientStorage").then(m => m.searchCreateOrUpdatePatient),
	findByMultipleCriteria: () => import("./patientStorage").then(m => m.findPatientByMultipleCriteria),
};

export const customFieldSync = {
	ccToAP: () => import("./customFieldSync").then(m => m.syncCCtoAPCustomFields),
	apToCC: () => import("./customFieldSync").then(m => m.syncAPtoCCCustomFields),
	appointmentToAP: () => import("./customFieldSync").then(m => m.syncAppointmentCustomFieldsToAP),
};

export const appointmentNotes = {
	create: () => import("./appointmentNotes").then(m => m.createAppointmentNote),
	update: () => import("./appointmentNotes").then(m => m.updateAppointmentNote),
	delete: () => import("./appointmentNotes").then(m => m.deleteAppointmentNote),
	updateAppointmentWithNoteId: () => import("./appointmentNotes").then(m => m.updateAppointmentWithNoteId),
};

export const financialData = {
	getInvoices: () => import("./financialData").then(m => m.getPatientInvoices),
	getPayments: () => import("./financialData").then(m => m.getPatientPayments),
	calculateMetrics: () => import("./financialData").then(m => m.calculatePatientFinancialMetrics),
	generateCustomFields: () => import("./financialData").then(m => m.generateFinancialCustomFields),
};

/**
 * Storage utilities namespace
 *
 * Provides organized access to all storage functionality with clear
 * categorization for different types of operations.
 */
export const storage = {
	patient: patientStorage,
	customFields: customFieldSync,
	notes: appointmentNotes,
	financial: financialData,
};

/**
 * Legacy compatibility exports
 *
 * These exports provide compatibility with v3Integration naming conventions
 * to ease migration and maintain familiar function names.
 */
export const legacyCompat = {
	// Equivalent to v3Integration's Contact.searchCreateOrUpdate
	searchCreateOrUpdate: searchCreateOrUpdatePatient,
	
	// Equivalent to v3Integration's syncCCtoAPCustomfields
	syncCCtoAPCustomfields: syncCCtoAPCustomFields,
	
	// Equivalent to v3Integration's syncApToCcCustomfields
	syncApToCcCustomfields: syncAPtoCCCustomFields,
	
	// Equivalent to v3Integration's updateOrCreateContact
	updateOrCreateContact: searchCreateOrUpdatePatient,
};

/**
 * Storage operation types for type safety
 */
export type StorageOperation = 
	| "patient_upsert"
	| "custom_field_sync_cc_to_ap"
	| "custom_field_sync_ap_to_cc"
	| "appointment_note_create"
	| "appointment_note_update"
	| "appointment_note_delete"
	| "financial_data_retrieval"
	| "financial_metrics_calculation";

/**
 * Storage result interface for consistent return types
 */
export interface StorageResult<T = any> {
	success: boolean;
	message: string;
	data?: T;
	error?: Error;
	operation: StorageOperation;
	timestamp: Date;
}

/**
 * Creates a standardized storage result
 *
 * @param operation - Type of storage operation
 * @param success - Whether the operation succeeded
 * @param message - Human-readable message
 * @param data - Optional result data
 * @param error - Optional error object
 * @returns Standardized storage result
 */
export function createStorageResult<T>(
	operation: StorageOperation,
	success: boolean,
	message: string,
	data?: T,
	error?: Error,
): StorageResult<T> {
	return {
		success,
		message,
		data,
		error,
		operation,
		timestamp: new Date(),
	};
}

/**
 * Storage configuration interface
 */
export interface StorageConfig {
	enableCustomFieldSync: boolean;
	enableAppointmentNotes: boolean;
	enableFinancialData: boolean;
	customFieldSyncBatchSize: number;
	noteCreationTimeout: number;
	financialDataCacheTime: number;
}

/**
 * Default storage configuration
 */
export const defaultStorageConfig: StorageConfig = {
	enableCustomFieldSync: true,
	enableAppointmentNotes: true,
	enableFinancialData: true,
	customFieldSyncBatchSize: 50,
	noteCreationTimeout: 30000, // 30 seconds
	financialDataCacheTime: 300000, // 5 minutes
};

/**
 * Storage utilities for common operations
 */
export const storageUtils = {
	/**
	 * Validates patient data before storage operations
	 */
	validatePatientData: (patientData: any): boolean => {
		return !!(patientData && (patientData.email || patientData.phone));
	},

	/**
	 * Generates a unique operation ID for tracking
	 */
	generateOperationId: (): string => {
		return `storage_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	},

	/**
	 * Formats error messages for storage operations
	 */
	formatStorageError: (operation: StorageOperation, error: Error): string => {
		return `Storage operation '${operation}' failed: ${error.message}`;
	},

	/**
	 * Checks if a storage operation should be retried
	 */
	shouldRetryOperation: (error: Error): boolean => {
		// Retry on network errors, timeouts, and temporary failures
		const retryableErrors = ['ECONNRESET', 'ETIMEDOUT', 'ENOTFOUND', 'TEMPORARY'];
		return retryableErrors.some(errorType => 
			error.message.includes(errorType) || error.name.includes(errorType)
		);
	},
};

/**
 * Storage metrics for monitoring and debugging
 */
export interface StorageMetrics {
	operationsCount: Record<StorageOperation, number>;
	successRate: Record<StorageOperation, number>;
	averageExecutionTime: Record<StorageOperation, number>;
	lastOperationTime: Record<StorageOperation, Date>;
}

/**
 * Storage metrics tracker (in-memory for Cloudflare Workers)
 */
class StorageMetricsTracker {
	private metrics: StorageMetrics = {
		operationsCount: {} as Record<StorageOperation, number>,
		successRate: {} as Record<StorageOperation, number>,
		averageExecutionTime: {} as Record<StorageOperation, number>,
		lastOperationTime: {} as Record<StorageOperation, Date>,
	};

	recordOperation(operation: StorageOperation, success: boolean, executionTime: number): void {
		// Update counts
		this.metrics.operationsCount[operation] = (this.metrics.operationsCount[operation] || 0) + 1;
		
		// Update success rate
		const currentCount = this.metrics.operationsCount[operation];
		const currentSuccessRate = this.metrics.successRate[operation] || 0;
		this.metrics.successRate[operation] = 
			((currentSuccessRate * (currentCount - 1)) + (success ? 1 : 0)) / currentCount;
		
		// Update average execution time
		const currentAvgTime = this.metrics.averageExecutionTime[operation] || 0;
		this.metrics.averageExecutionTime[operation] = 
			((currentAvgTime * (currentCount - 1)) + executionTime) / currentCount;
		
		// Update last operation time
		this.metrics.lastOperationTime[operation] = new Date();
	}

	getMetrics(): StorageMetrics {
		return { ...this.metrics };
	}

	resetMetrics(): void {
		this.metrics = {
			operationsCount: {} as Record<StorageOperation, number>,
			successRate: {} as Record<StorageOperation, number>,
			averageExecutionTime: {} as Record<StorageOperation, number>,
			lastOperationTime: {} as Record<StorageOperation, Date>,
		};
	}
}

// Export singleton metrics tracker
export const storageMetrics = new StorageMetricsTracker();
