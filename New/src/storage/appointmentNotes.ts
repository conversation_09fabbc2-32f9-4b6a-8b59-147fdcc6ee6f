/**
 * Appointment note management for DermaCare bi-directional sync service
 *
 * This module provides comprehensive appointment note handling functionality
 * that integrates with the AutoPatient (AP) platform's note system. It manages
 * the creation, updating, and deletion of notes associated with appointments
 * to maintain proper documentation and communication records.
 *
 * **Key Features:**
 * - Automatic note creation for appointment events (booked, updated, cancelled)
 * - Note content generation based on appointment data and status changes
 * - Integration with AP note API for persistent storage
 * - Note ID tracking in appointment records (apNoteID field)
 * - Comprehensive error handling and logging
 *
 * **Note Types:**
 * - Appointment booking confirmations
 * - Appointment modification records
 * - Cancellation notifications
 * - Service and provider change logs
 * - Custom appointment-related communications
 *
 * **Integration Points:**
 * - Links with appointment processors for automatic note creation
 * - Connects to AP note API for CRUD operations
 * - Updates appointment records with note references
 * - Supports custom field synchronization for note metadata
 *
 * @example
 * ```typescript
 * // Create note for new appointment
 * const noteId = await createAppointmentNote(appointmentData, "booked");
 *
 * // Update note for appointment changes
 * await updateAppointmentNote(noteId, appointmentData, "updated");
 *
 * // Delete note when appointment is cancelled
 * await deleteAppointmentNote(noteId);
 * ```
 *
 * @since 1.0.0
 * @version 1.0.0
 */

import { apClient } from "@api/apClient";
import { getDb } from "@database";
import { appointment } from "@database/schema";
import type {
	GetAPAppointmentType,
	GetCCAppointmentType,
} from "@type";
import { logError } from "@utils/errorLogger";
import { eq } from "drizzle-orm";

/**
 * Appointment note event types
 */
export type AppointmentNoteEvent = 
	| "booked" 
	| "updated" 
	| "cancelled" 
	| "completed" 
	| "no_show" 
	| "rescheduled";

/**
 * Note content template interface
 */
export interface NoteTemplate {
	title: string;
	content: string;
	tags?: string[];
}

/**
 * Creates a note for an appointment in the AP platform
 *
 * This function generates appropriate note content based on the appointment
 * data and event type, then creates the note in AP and updates the local
 * appointment record with the note ID for future reference.
 *
 * **Note Content Generation:**
 * - Includes appointment details (date, time, services, provider)
 * - Adds event-specific information (booking confirmation, changes, etc.)
 * - Formats content for readability and professional appearance
 * - Includes relevant metadata and tracking information
 *
 * **Error Handling:**
 * - Validates appointment and contact data before processing
 * - Handles AP API failures gracefully
 * - Logs detailed error information for debugging
 * - Returns null on failure to prevent blocking appointment processing
 *
 * @param appointmentData - Complete appointment data (CC or AP format)
 * @param contactId - AP contact ID for note association
 * @param event - Type of appointment event triggering note creation
 * @param customContent - Optional custom content to include in note
 *
 * @returns Promise resolving to note ID or null if creation failed
 *
 * @example
 * ```typescript
 * // Create booking confirmation note
 * const noteId = await createAppointmentNote(
 *   ccAppointmentData,
 *   "ap_contact_123",
 *   "booked"
 * );
 *
 * // Create cancellation note with custom message
 * const noteId = await createAppointmentNote(
 *   appointmentData,
 *   contactId,
 *   "cancelled",
 *   "Cancelled due to patient request"
 * );
 * ```
 */
export async function createAppointmentNote(
	appointmentData: GetCCAppointmentType | GetAPAppointmentType,
	contactId: string,
	event: AppointmentNoteEvent,
	customContent?: string,
): Promise<string | null> {
	try {
		console.log(`Creating appointment note for contact: ${contactId}, event: ${event}`);

		// Generate note content based on appointment data and event
		const noteTemplate = generateNoteTemplate(appointmentData, event, customContent);

		// Create note in AP platform (using body field as AP API expects)
		const noteBody = `${noteTemplate.title}\n\n${noteTemplate.content}`;
		const note = await apClient.note.create(contactId, noteBody);

		if (note && note.id) {
			console.log(`Appointment note created successfully: ${note.id}`);
			return note.id;
		} else {
			console.error(`Failed to create appointment note for contact: ${contactId}`);
			return null;
		}
	} catch (error) {
		await logError(
			"APPOINTMENT_NOTE_CREATE_ERROR",
			error,
			{
				contactId,
				event,
				appointmentId: appointmentData.id,
			},
			"AppointmentNotes",
		);
		return null;
	}
}

/**
 * Updates an existing appointment note with new information
 *
 * This function modifies an existing note to reflect changes in appointment
 * status, details, or other relevant information. It's typically called when
 * appointments are updated or their status changes.
 *
 * @param noteId - ID of the existing note to update
 * @param appointmentData - Updated appointment data
 * @param event - Type of event triggering the update
 * @param customContent - Optional custom content to add
 *
 * @returns Promise resolving to success status
 */
export async function updateAppointmentNote(
	noteId: string,
	appointmentData: GetCCAppointmentType | GetAPAppointmentType,
	event: AppointmentNoteEvent,
	customContent?: string,
): Promise<boolean> {
	try {
		console.log(`Updating appointment note: ${noteId}, event: ${event}`);

		// Generate updated note content
		const noteTemplate = generateNoteTemplate(appointmentData, event, customContent);

		// Update note in AP platform (need contactId for AP API)
		// Note: AP API requires contactId for note updates, so we need to get it from appointment data
		const contactId = getContactIdFromAppointment(appointmentData);
		if (!contactId) {
			console.error(`Cannot update note ${noteId}: missing contact ID`);
			return false;
		}

		const noteBody = `${noteTemplate.title}\n\n${noteTemplate.content}`;
		const updatedNote = await apClient.note.update(contactId, noteId, noteBody);

		if (updatedNote) {
			console.log(`Appointment note updated successfully: ${noteId}`);
			return true;
		} else {
			console.error(`Failed to update appointment note: ${noteId}`);
			return false;
		}
	} catch (error) {
		await logError(
			"APPOINTMENT_NOTE_UPDATE_ERROR",
			error,
			{
				noteId,
				event,
				appointmentId: appointmentData.id,
			},
			"AppointmentNotes",
		);
		return false;
	}
}

/**
 * Deletes an appointment note from the AP platform
 *
 * This function removes a note when it's no longer needed, typically when
 * appointments are permanently cancelled or deleted from the system.
 *
 * @param noteId - ID of the note to delete
 * @param contactId - Contact ID (required by AP API)
 *
 * @returns Promise resolving to success status
 */
export async function deleteAppointmentNote(noteId: string, contactId: string): Promise<boolean> {
	try {
		console.log(`Deleting appointment note: ${noteId}`);

		// Delete note from AP platform
		const success = await apClient.note.delete(contactId, noteId);

		if (success) {
			console.log(`Appointment note deleted successfully: ${noteId}`);
			return true;
		} else {
			console.error(`Failed to delete appointment note: ${noteId}`);
			return false;
		}
	} catch (error) {
		await logError(
			"APPOINTMENT_NOTE_DELETE_ERROR",
			error,
			{ noteId, contactId },
			"AppointmentNotes",
		);
		return false;
	}
}

/**
 * Updates appointment record with note ID
 *
 * This function stores the note ID in the appointment record's apNoteID field
 * for future reference and management operations.
 *
 * @param appointmentId - Internal appointment ID
 * @param noteId - AP note ID to store
 *
 * @returns Promise resolving to success status
 */
export async function updateAppointmentWithNoteId(
	appointmentId: string,
	noteId: string,
): Promise<boolean> {
	try {
		const db = getDb();

		await db
			.update(appointment)
			.set({
				apNoteID: noteId,
				updatedAt: new Date(),
			})
			.where(eq(appointment.id, appointmentId));

		console.log(`Appointment ${appointmentId} updated with note ID: ${noteId}`);
		return true;
	} catch (error) {
		await logError(
			"APPOINTMENT_NOTE_ID_UPDATE_ERROR",
			error,
			{ appointmentId, noteId },
			"AppointmentNotes",
		);
		return false;
	}
}

/**
 * Generates note content template based on appointment data and event
 *
 * This function creates appropriate note titles and content based on the
 * appointment information and the type of event that occurred.
 *
 * @param appointmentData - Appointment data (CC or AP format)
 * @param event - Type of appointment event
 * @param customContent - Optional custom content to include
 *
 * @returns Note template with title, content, and tags
 */
function generateNoteTemplate(
	appointmentData: GetCCAppointmentType | GetAPAppointmentType,
	event: AppointmentNoteEvent,
	customContent?: string,
): NoteTemplate {
	// Extract date based on appointment type
	const isCC = 'startsAt' in appointmentData;
	const appointmentDateString = isCC
		? (appointmentData as GetCCAppointmentType).startsAt
		: (appointmentData as GetAPAppointmentType).startTime;
	const appointmentDate = new Date(appointmentDateString || new Date());
	const formattedDate = appointmentDate.toLocaleDateString();
	const formattedTime = appointmentDate.toLocaleTimeString();

	// Extract relevant fields based on appointment type
	const services = isCC
		? (appointmentData as GetCCAppointmentType).services?.join(', ') || 'Not specified'
		: (appointmentData as GetAPAppointmentType).calendarId || 'Not specified';

	const provider = isCC
		? (appointmentData as GetCCAppointmentType).title || 'Not specified'
		: (appointmentData as GetAPAppointmentType).assignedUserId || 'Not specified';

	// Generate event-specific content
	let title: string;
	let content: string;
	const tags: string[] = ['appointment', event];

	switch (event) {
		case "booked":
			title = `Appointment Booked - ${formattedDate}`;
			content = `
**Appointment Confirmation**

📅 **Date:** ${formattedDate}
🕐 **Time:** ${formattedTime}
👨‍⚕️ **Provider:** ${provider}
🏥 **Services:** ${services}

Your appointment has been successfully booked. Please arrive 15 minutes early for check-in.

${customContent ? `\n**Additional Notes:**\n${customContent}` : ''}
			`.trim();
			break;

		case "updated":
			title = `Appointment Updated - ${formattedDate}`;
			content = `
**Appointment Modification**

📅 **Date:** ${formattedDate}
🕐 **Time:** ${formattedTime}
👨‍⚕️ **Provider:** ${provider}
🏥 **Services:** ${services}

Your appointment details have been updated. Please review the new information above.

${customContent ? `\n**Changes Made:**\n${customContent}` : ''}
			`.trim();
			break;

		case "cancelled":
			title = `Appointment Cancelled - ${formattedDate}`;
			content = `
**Appointment Cancellation**

The following appointment has been cancelled:

📅 **Date:** ${formattedDate}
🕐 **Time:** ${formattedTime}
👨‍⚕️ **Provider:** ${provider}
🏥 **Services:** ${services}

${customContent ? `\n**Cancellation Reason:**\n${customContent}` : ''}

Please contact us to reschedule if needed.
			`.trim();
			break;

		case "completed":
			title = `Appointment Completed - ${formattedDate}`;
			content = `
**Appointment Completed**

📅 **Date:** ${formattedDate}
🕐 **Time:** ${formattedTime}
👨‍⚕️ **Provider:** ${provider}
🏥 **Services:** ${services}

Your appointment has been completed successfully.

${customContent ? `\n**Visit Notes:**\n${customContent}` : ''}
			`.trim();
			break;

		case "no_show":
			title = `No Show - ${formattedDate}`;
			content = `
**Missed Appointment**

📅 **Date:** ${formattedDate}
🕐 **Time:** ${formattedTime}
👨‍⚕️ **Provider:** ${provider}
🏥 **Services:** ${services}

Patient did not attend the scheduled appointment.

${customContent ? `\n**Notes:**\n${customContent}` : ''}
			`.trim();
			tags.push('no-show');
			break;

		case "rescheduled":
			title = `Appointment Rescheduled - ${formattedDate}`;
			content = `
**Appointment Rescheduled**

📅 **Original Date:** ${formattedDate}
🕐 **Original Time:** ${formattedTime}
👨‍⚕️ **Provider:** ${provider}
🏥 **Services:** ${services}

This appointment has been rescheduled to a new date and time.

${customContent ? `\n**Reschedule Details:**\n${customContent}` : ''}
			`.trim();
			break;

		default:
			title = `Appointment Note - ${formattedDate}`;
			content = `
**Appointment Information**

📅 **Date:** ${formattedDate}
🕐 **Time:** ${formattedTime}
👨‍⚕️ **Provider:** ${provider}
🏥 **Services:** ${services}

${customContent || 'Appointment note created.'}
			`.trim();
	}

	return {
		title,
		content,
		tags,
	};
}

/**
 * Extracts contact ID from appointment data
 *
 * Helper function to get the contact ID from either CC or AP appointment data
 * for use with AP note operations.
 *
 * @param appointmentData - Appointment data (CC or AP format)
 * @returns Contact ID or null if not found
 */
function getContactIdFromAppointment(
	appointmentData: GetCCAppointmentType | GetAPAppointmentType,
): string | null {
	// Check if this is AP appointment data (has contactId)
	if ('contactId' in appointmentData) {
		return (appointmentData as GetAPAppointmentType).contactId;
	}

	// For CC appointment data, we would need to look up the patient record
	// to get the AP contact ID. This is a limitation of the current approach.
	// In practice, this function should be called with the contact ID already known.
	console.warn('Cannot extract contact ID from CC appointment data directly');
	return null;
}
