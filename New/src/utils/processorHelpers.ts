/**
 * Processor Helper Utilities for DermaCare Data Sync
 * 
 * This module provides common utility functions for webhook processors to reduce
 * code duplication and standardize error handling, buffer management, and response
 * formatting across all processor implementations.
 * 
 * **Key Features:**
 * - Standardized error handling with automatic logging
 * - Common response formatting for processor results
 * - Buffer management utilities for duplicate prevention
 * - Database operation helpers
 * - Validation utilities for processor inputs
 * 
 * **Usage:**
 * ```typescript
* import { 
 *   handleProcessorError, 
 *   createSuccessResponse, 
 *   createErrorResponse,
 *   withBufferCheck 
 * } from '@utils/processorHelpers';
 * 
 * // Use in processor functions
 * export async function processPatient(payload, context) {
 *   return withBufferCheck(
 *     generatePatientBufferKey("create", payload.id),
 *     async () => {
 *       // Processing logic here
 *       return createSuccessResponse("Patient processed successfully");
 *     },
 *     (error) => handleProcessorError(error, payload.id, "CC", "PatientProcessor")
 *   );
 * }
 *
```
 * 
 * @since 1.0.0
 * @version 1.0.0
 */

import { checkAndAddTo<PERSON>uffer } from "@utils/bufferManager";
import { logError, logSyncError } from "@utils/errorLogger";

/**
 * Standard processor response interface
 */
export interface ProcessorResponse {
	success: boolean;
	message: string;
	skipped?: boolean;
}

/**
 * Creates a standardized success response
 *
 * @param message - Success message
 * @returns Standardized success response
 */
export function createSuccessResponse(message: string): ProcessorResponse {
	return {
		success: true,
		message,
	};
}

/**
 * Creates a standardized error response
 *
 * @param message - Error message
 * @param skipped - Whether the operation was skipped
 * @returns Standardized error response
 */
export function createErrorResponse(
	message: string,
	skipped: boolean = false,
): ProcessorResponse {
	return {
		success: false,
		message,
		skipped,
	};
}

/**
 * Creates a standardized skipped response for buffer hits
 *
 * @param operation - Operation that was skipped
 * @param entityId - ID of the entity
 * @returns Standardized skipped response
 */
export function createSkippedResponse(
	operation: string,
	entityId: string | number,
): ProcessorResponse {
	return {
		success: true,
		message: `${operation} recently completed, skipping. Entity ID: ${entityId}`,
		skipped: true,
	};
}

/**
 * Handles processor errors with standardized logging and response formatting
 *
 * @param error - Error object or message
 * @param entityId - ID of the entity being processed
 * @param platform - Platform where error occurred (CC or AP)
 * @param service - Service/processor name
 * @param errorType - Specific error type for logging
 * @param context - Additional context for error logging
 * @returns Standardized error response
 */
export async function handleProcessorError(
	error: unknown,
	entityId: string | number,
	platform: "CC" | "AP",
	service: string,
	errorType?: string,
	context?: Record<string, unknown>,
): Promise<ProcessorResponse> {
	const errorMessage = error instanceof Error ? error.message : String(error);
	const logType = errorType || `${service.toUpperCase()}_ERROR`;

	// Log the error
	if (platform === "CC" || platform === "AP") {
		await logSyncError(logType, error, entityId, platform, service);
	} else {
		await logError(logType, error, { entityId, platform, ...context }, service);
	}

	return createErrorResponse(`Error in ${service}: ${errorMessage}`);
}

/**
 * Handles API operation errors with standardized logging
 *
 * @param error - Error object
 * @param operation - API operation that failed
 * @param entityId - ID of the entity
 * @param platform - Platform (CC or AP)
 * @param service - Service name
 * @returns Standardized error response
 */
export async function handleApiError(
	error: unknown,
	operation: string,
	entityId: string | number,
	platform: "CC" | "AP",
	service: string,
): Promise<ProcessorResponse> {
	const errorMessage = error instanceof Error ? error.message : String(error);
	const errorType = `${operation.toUpperCase()}_${platform}_FAILED`;

	await logSyncError(errorType, error, entityId, platform, service);

	return createErrorResponse(
		`Failed to ${operation} in ${platform}: ${errorMessage}`,
	);
}

/**
 * Wraps a processor function with buffer checking to prevent duplicate processing
 *
 * @param bufferKey - Buffer key for duplicate checking
 * @param processorFn - Processor function to execute
 * @param errorHandler - Optional error handler function
 * @returns Processor response
 */
export async function withBufferCheck<T extends ProcessorResponse>(
	bufferKey: string,
	processorFn: () => Promise<T>,
	errorHandler?: (error: unknown) => Promise<ProcessorResponse>,
): Promise<T | ProcessorResponse> {
	// Check buffer to prevent duplicate processing
	if (checkAndAddToBuffer(bufferKey)) {
		return createSkippedResponse("Operation", "buffer-check") as T;
	}

	try {
		return await processorFn();
	} catch (error) {
		if (errorHandler) {
			return await errorHandler(error);
		}
		throw error;
	}
}

/**
 * Validates that required fields are present in payload
 *
 * @param payload - Payload object to validate
 * @param requiredFields - Array of required field names
 * @param entityType - Type of entity for error messages
 * @returns Validation result with error response if invalid
 */
export function validateRequiredFields(
	payload: Record<string, unknown>,
	requiredFields: string[],
	entityType: string = "entity",
): { isValid: boolean; response?: ProcessorResponse } {
	const missingFields = requiredFields.filter(
		(field) =>
			!payload[field] ||
			(typeof payload[field] === "string" &&
				(payload[field] as string).trim() === ""),
	);

	if (missingFields.length > 0) {
		return {
			isValid: false,
			response: createErrorResponse(
				`Missing required fields for ${entityType}: ${missingFields.join(", ")}`,
			),
		};
	}

	return { isValid: true };
}

/**
 * Validates that either email or phone is present (common requirement)
 *
 * @param payload - Payload with email/phone fields
 * @param entityId - Entity ID for error messages
 * @param entityType - Type of entity for error messages
 * @returns Validation result with error response if invalid
 */
export function validateEmailOrPhone(
	payload: { email?: string; phone?: string; phoneMobile?: string },
	entityId: string | number,
	entityType: string = "contact",
): { isValid: boolean; response?: ProcessorResponse } {
	const hasEmail = payload.email && payload.email.trim() !== "";
	const hasPhone =
		(payload.phone && payload.phone.trim() !== "") ||
		(payload.phoneMobile && payload.phoneMobile.trim() !== "");

	if (!hasEmail && !hasPhone) {
		return {
			isValid: false,
			response: createErrorResponse(
				`Email and phone are empty, cannot process ${entityType}. ID: ${entityId}`,
			),
		};
	}

	return { isValid: true };
}

/**
 * Safely extracts error message from various error types
 *
 * @param error - Error object or message
 * @returns Clean error message string
 */
export function extractErrorMessage(error: unknown): string {
	if (error instanceof Error) {
		return error.message;
	}
	if (typeof error === "string") {
		return error;
	}
	if (error && typeof error === "object" && "message" in error) {
		return String((error as { message: unknown }).message);
	}
	return "Unknown error";
}

/**
 * Creates a standardized log message for processor operations
 *
 * @param operation - Operation being performed
 * @param entityType - Type of entity
 * @param entityId - Entity ID
 * @param platform - Platform (CC/AP)
 * @param success - Whether operation was successful
 * @returns Formatted log message
 */
export function createLogMessage(
	operation: string,
	entityType: string,
	entityId: string | number,
	platform: "CC" | "AP",
	success: boolean = true,
): string {
	const status = success ? "successfully" : "failed to";
	return `${operation} ${entityType} ${status} in ${platform}. ID: ${entityId}`;
}

/**
 * Checks if a database result indicates the entity was found
 *
 * @param dbResult - Database query result
 * @param entityType - Type of entity for error messages
 * @param entityId - Entity ID for error messages
 * @returns Validation result with error response if not found
 */
export function validateDatabaseResult<T>(
	dbResult: T | undefined | null,
	entityType: string,
	entityId: string | number,
): { isValid: boolean; response?: ProcessorResponse; data?: T } {
	if (!dbResult) {
		return {
			isValid: false,
			response: createErrorResponse(
				`${entityType} not found in database. ID: ${entityId}`,
			),
		};
	}

	return { isValid: true, data: dbResult };
}
