/**
 * Legacy socket event type (for reference) - removed as it's not used
 */

/**
 * New webhook event structure
 * Replaces socket events with standardized webhook format
 */
export interface WebhookEvent {
	/** Event type (e.g., "EntityWasCreated", "EntityWasUpdated", "EntityWasDeleted") */
	event:
		| "EntityWasCreated"
		| "EntityWasUpdated"
		| "EntityWasDeleted"
		| "AppointmentWasCreated"
		| "AppointmentWasUpdated"
		| "AppointmentWasDeleted";
	/** Model type (e.g., "Patient", "Appointment", "Invoice", "Payment") */
	model: "Patient" | "Appointment" | "Invoice" | "Payment" | "Service";
	/** Entity ID */
	id: number;
	/** Entity data payload */
	payload: Record<string, unknown>;
	/** Optional timestamp */
	timestamp?: string;
}

/**
 * Webhook processing context
 */
export interface WebhookContext {
	/** Webhook event data */
	event: WebhookEvent;
	/** Processing timestamp */
	processedAt: Date;
	/** Request ID for tracking */
	requestId: string;
}

// KeyValue type removed as it's not used

export * from "./APTypes";
export * from "./CCTypes";
