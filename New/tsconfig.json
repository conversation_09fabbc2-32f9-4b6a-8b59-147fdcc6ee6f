{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "skipLibCheck": true, "lib": ["ESNext"], "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@api": ["./src/api"], "@api/*": ["./src/api/*"], "@database": ["./src/database"], "@database/*": ["./src/database/*"], "@storage": ["./src/storage"], "@storage/*": ["./src/storage/*"], "@config": ["./src/utils/configs"], "@utils": ["./src/utils"], "@utils/*": ["./src/utils/*"], "@helpers": ["./src/helpers"], "@helpers/*": ["./src/helpers/*"], "@type": ["./src/type"], "@type/*": ["./src/type/*"]}}, "exclude": ["old"]}